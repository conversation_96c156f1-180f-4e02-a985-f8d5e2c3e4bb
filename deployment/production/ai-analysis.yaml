# AI分析服务生产环境部署配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-analysis-config
  namespace: ai-pm-prod
data:
  config.yaml: |
    # AI分析服务配置
    app:
      name: ai-analysis
      version: v1.0.0
      environment: production
      debug: false
    
    # 服务器配置
    server:
      host: 0.0.0.0
      port: 8000
      workers: 4
      timeout: 300
      keepalive: 2
    
    # 数据库配置
    database:
      pool_size: 20
      max_overflow: 30
      pool_timeout: 30
      pool_recycle: 3600
      echo: false
    
    # Redis配置
    redis:
      max_connections: 20
      retry_on_timeout: true
      socket_timeout: 5
      socket_connect_timeout: 5
    
    # AI模型配置
    ai:
      models:
        progress_prediction:
          model_path: /app/models/progress_model.pkl
          confidence_threshold: 0.7
          update_interval: 3600  # 1小时
        risk_analysis:
          model_path: /app/models/risk_model.pkl
          risk_threshold: 0.8
          update_interval: 1800  # 30分钟
        team_efficiency:
          model_path: /app/models/efficiency_model.pkl
          analysis_window: 30  # 30天
          update_interval: 86400  # 24小时
      
      # 模型训练配置
      training:
        batch_size: 32
        epochs: 100
        learning_rate: 0.001
        validation_split: 0.2
        early_stopping_patience: 10
    
    # 缓存配置
    cache:
      default_timeout: 3600
      key_prefix: "ai-pm:ai:"
      prediction_cache_timeout: 1800
      analysis_cache_timeout: 3600
    
    # 日志配置
    logging:
      level: INFO
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      file: /app/logs/ai-analysis.log
      max_size: 100MB
      backup_count: 10
    
    # 监控配置
    monitoring:
      metrics_enabled: true
      health_check_interval: 30
      performance_tracking: true

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-analysis
  namespace: ai-pm-prod
  labels:
    app: ai-analysis
    version: v1.0.0
    component: ai-service
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ai-analysis
  template:
    metadata:
      labels:
        app: ai-analysis
        version: v1.0.0
        component: ai-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: ai-pm-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: ai-analysis
        image: ai-pm/ai-analysis:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        - name: MODEL_STORAGE_PATH
          value: "/app/models"
        - name: PYTHONPATH
          value: "/app"
        - name: WORKERS
          value: "4"
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
            ephemeral-storage: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
            ephemeral-storage: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 60
          successThreshold: 1
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: models
          mountPath: /app/models
        - name: logs
          mountPath: /app/logs
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: ai-analysis-config
      - name: models
        persistentVolumeClaim:
          claimName: ai-models-pvc
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      - name: tmp
        emptyDir:
          sizeLimit: 1Gi
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ai-analysis
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - compute-optimized

---
apiVersion: v1
kind: Service
metadata:
  name: ai-analysis-service
  namespace: ai-pm-prod
  labels:
    app: ai-analysis
    component: ai-service
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  selector:
    app: ai-analysis
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  sessionAffinity: None

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-models-pvc
  namespace: ai-pm-prod
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ai-analysis-pdb
  namespace: ai-pm-prod
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: ai-analysis

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-analysis-hpa
  namespace: ai-pm-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-analysis
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 75
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
# AI模型训练任务
apiVersion: batch/v1
kind: CronJob
metadata:
  name: ai-model-training
  namespace: ai-pm-prod
spec:
  schedule: "0 2 * * 0"  # 每周日凌晨2点执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: model-trainer
            image: ai-pm/ai-analysis:v1.0.0
            command: ["python", "/app/scripts/train_models.py"]
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-credentials
                  key: url
            - name: MODEL_STORAGE_PATH
              value: "/app/models"
            resources:
              requests:
                cpu: 2000m
                memory: 4Gi
              limits:
                cpu: 4000m
                memory: 8Gi
            volumeMounts:
            - name: models
              mountPath: /app/models
          volumes:
          - name: models
            persistentVolumeClaim:
              claimName: ai-models-pvc
          restartPolicy: OnFailure
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
