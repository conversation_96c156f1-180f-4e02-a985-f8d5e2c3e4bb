# 项目管理服务生产环境部署配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: project-management-config
  namespace: ai-pm-prod
data:
  application-production.yml: |
    server:
      port: 8081
      servlet:
        context-path: /api/v1/projects
    
    spring:
      application:
        name: project-management
      profiles:
        active: production
      
      datasource:
        hikari:
          maximum-pool-size: 25
          minimum-idle: 5
          connection-timeout: 30000
          idle-timeout: 600000
          max-lifetime: 1800000
          leak-detection-threshold: 60000
      
      jpa:
        hibernate:
          ddl-auto: validate
        show-sql: false
        properties:
          hibernate:
            dialect: org.hibernate.dialect.PostgreSQLDialect
            format_sql: false
            use_sql_comments: false
            jdbc:
              batch_size: 20
              order_inserts: true
              order_updates: true
      
      redis:
        timeout: 2000ms
        lettuce:
          pool:
            max-active: 25
            max-idle: 10
            min-idle: 5
    
    # 缓存配置
    cache:
      redis:
        time-to-live: 3600000  # 1小时
        key-prefix: "ai-pm:project:"
        enable-statistics: true
    
    # 文件上传配置
    file:
      upload:
        max-size: 50MB
        allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip
        storage-path: /app/uploads
    
    # 日志配置
    logging:
      level:
        com.aipm: INFO
        org.springframework.security: WARN
        org.springframework.web: WARN
        org.hibernate: WARN
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      file:
        name: /app/logs/project-management.log
        max-size: 100MB
        max-history: 30
    
    # 监控配置
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
          base-path: /actuator
      endpoint:
        health:
          show-details: when-authorized
          probes:
            enabled: true
        metrics:
          enabled: true
        prometheus:
          enabled: true
      metrics:
        export:
          prometheus:
            enabled: true
        tags:
          application: project-management
          environment: production

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: project-management
  namespace: ai-pm-prod
  labels:
    app: project-management
    version: v1.0.0
    component: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: project-management
  template:
    metadata:
      labels:
        app: project-management
        version: v1.0.0
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      serviceAccountName: ai-pm-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: project-management
        image: ai-pm/project-management:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8081
          name: http
          protocol: TCP
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: password
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: JAVA_OPTS
          value: "-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
            ephemeral-storage: 2Gi
          limits:
            cpu: 1000m
            memory: 2Gi
            ephemeral-storage: 4Gi
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          successThreshold: 1
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: project-management-config
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      - name: uploads
        persistentVolumeClaim:
          claimName: project-uploads-pvc
      - name: tmp
        emptyDir:
          sizeLimit: 500Mi
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - project-management
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: project-management-service
  namespace: ai-pm-prod
  labels:
    app: project-management
    component: backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8081"
    prometheus.io/path: "/actuator/prometheus"
spec:
  type: ClusterIP
  selector:
    app: project-management
  ports:
  - name: http
    port: 8081
    targetPort: 8081
    protocol: TCP
  sessionAffinity: None

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: project-uploads-pvc
  namespace: ai-pm-prod
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: nfs-storage

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: project-management-pdb
  namespace: ai-pm-prod
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: project-management

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: project-management-hpa
  namespace: ai-pm-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: project-management
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
