#!/bin/bash

# AI项目管理平台UAT环境部署脚本
# 版本: v1.0
# 创建时间: 2025-08-28
# 描述: 自动化部署UAT测试环境，包括数据库初始化、服务部署和测试数据准备

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
UAT_NAMESPACE="ai-pm-uat"
UAT_DOMAIN="uat.ai-pm-test.com"
DATABASE_NAME="ai_pm_uat"
REDIS_DB="1"

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具和环境..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装，请先安装 kubectl"
        exit 1
    fi
    
    # 检查helm
    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装，请先安装 helm"
        exit 1
    fi
    
    # 检查docker
    if ! command -v docker &> /dev/null; then
        log_error "docker 未安装，请先安装 docker"
        exit 1
    fi
    
    log_success "所有必要工具检查完成"
}

# 创建命名空间
create_namespace() {
    log_info "创建UAT命名空间..."
    
    kubectl create namespace $UAT_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "命名空间 $UAT_NAMESPACE 创建完成"
}

# 部署数据库服务
deploy_database() {
    log_info "部署UAT数据库服务..."
    
    # PostgreSQL配置
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: $UAT_NAMESPACE
data:
  POSTGRES_DB: $DATABASE_NAME
  POSTGRES_USER: ai_pm_user
  POSTGRES_PASSWORD: uat_password_123
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-uat
  namespace: $UAT_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-uat
  template:
    metadata:
      labels:
        app: postgres-uat
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
        envFrom:
        - configMapRef:
            name: postgres-config
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: $UAT_NAMESPACE
spec:
  selector:
    app: postgres-uat
  ports:
  - port: 5432
    targetPort: 5432
EOF

    # Redis配置
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-uat
  namespace: $UAT_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-uat
  template:
    metadata:
      labels:
        app: redis-uat
    spec:
      containers:
      - name: redis
        image: redis:7
        ports:
        - containerPort: 6379
        command: ["redis-server"]
        args: ["--appendonly", "yes"]
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: $UAT_NAMESPACE
spec:
  selector:
    app: redis-uat
  ports:
  - port: 6379
    targetPort: 6379
EOF

    log_success "数据库服务部署完成"
}

# 等待数据库就绪
wait_for_database() {
    log_info "等待数据库服务就绪..."
    
    # 等待PostgreSQL就绪
    kubectl wait --for=condition=ready pod -l app=postgres-uat -n $UAT_NAMESPACE --timeout=300s
    
    # 等待Redis就绪
    kubectl wait --for=condition=ready pod -l app=redis-uat -n $UAT_NAMESPACE --timeout=300s
    
    log_success "数据库服务已就绪"
}

# 初始化数据库
initialize_database() {
    log_info "初始化UAT数据库..."
    
    # 获取PostgreSQL Pod名称
    POSTGRES_POD=$(kubectl get pods -n $UAT_NAMESPACE -l app=postgres-uat -o jsonpath='{.items[0].metadata.name}')
    
    # 执行数据库初始化脚本
    kubectl exec -n $UAT_NAMESPACE $POSTGRES_POD -- psql -U ai_pm_user -d $DATABASE_NAME -c "
    -- 创建用户管理表
    CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'USER',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建项目表
    CREATE TABLE IF NOT EXISTS projects (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        description TEXT,
        owner_id UUID REFERENCES users(id),
        status VARCHAR(20) DEFAULT 'ACTIVE',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建任务表
    CREATE TABLE IF NOT EXISTS tasks (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title VARCHAR(200) NOT NULL,
        description TEXT,
        project_id UUID REFERENCES projects(id),
        assignee_id UUID REFERENCES users(id),
        status VARCHAR(20) DEFAULT 'TODO',
        priority VARCHAR(10) DEFAULT 'MEDIUM',
        due_date TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    "
    
    log_success "数据库初始化完成"
}

# 部署应用服务
deploy_applications() {
    log_info "部署UAT应用服务..."
    
    # 用户管理服务
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management-uat
  namespace: $UAT_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-management-uat
  template:
    metadata:
      labels:
        app: user-management-uat
    spec:
      containers:
      - name: user-management
        image: ai-pm/user-management:uat
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "uat"
        - name: DATABASE_URL
          value: "******************************************************"
        - name: REDIS_URL
          value: "redis://redis-service:6379/$REDIS_DB"
---
apiVersion: v1
kind: Service
metadata:
  name: user-management-service
  namespace: $UAT_NAMESPACE
spec:
  selector:
    app: user-management-uat
  ports:
  - port: 8080
    targetPort: 8080
EOF

    # 项目管理服务
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: project-management-uat
  namespace: $UAT_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: project-management-uat
  template:
    metadata:
      labels:
        app: project-management-uat
    spec:
      containers:
      - name: project-management
        image: ai-pm/project-management:uat
        ports:
        - containerPort: 8081
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "uat"
        - name: DATABASE_URL
          value: "******************************************************"
---
apiVersion: v1
kind: Service
metadata:
  name: project-management-service
  namespace: $UAT_NAMESPACE
spec:
  selector:
    app: project-management-uat
  ports:
  - port: 8081
    targetPort: 8081
EOF

    # AI分析服务
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-analysis-uat
  namespace: $UAT_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-analysis-uat
  template:
    metadata:
      labels:
        app: ai-analysis-uat
    spec:
      containers:
      - name: ai-analysis
        image: ai-pm/ai-analysis:uat
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "uat"
        - name: DATABASE_URL
          value: "**************************************************************/$DATABASE_NAME"
---
apiVersion: v1
kind: Service
metadata:
  name: ai-analysis-service
  namespace: $UAT_NAMESPACE
spec:
  selector:
    app: ai-analysis-uat
  ports:
  - port: 8000
    targetPort: 8000
EOF

    # 前端应用
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-uat
  namespace: $UAT_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-uat
  template:
    metadata:
      labels:
        app: frontend-uat
    spec:
      containers:
      - name: frontend
        image: ai-pm/frontend:uat
        ports:
        - containerPort: 80
        env:
        - name: REACT_APP_API_BASE_URL
          value: "https://uat-api.ai-pm-test.com"
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: $UAT_NAMESPACE
spec:
  selector:
    app: frontend-uat
  ports:
  - port: 80
    targetPort: 80
EOF

    log_success "应用服务部署完成"
}

# 配置Ingress
configure_ingress() {
    log_info "配置UAT环境Ingress..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-pm-uat-ingress
  namespace: $UAT_NAMESPACE
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  tls:
  - hosts:
    - $UAT_DOMAIN
    - uat-api.ai-pm-test.com
    secretName: ai-pm-uat-tls
  rules:
  - host: $UAT_DOMAIN
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  - host: uat-api.ai-pm-test.com
    http:
      paths:
      - path: /api/v1/users
        pathType: Prefix
        backend:
          service:
            name: user-management-service
            port:
              number: 8080
      - path: /api/v1/projects
        pathType: Prefix
        backend:
          service:
            name: project-management-service
            port:
              number: 8081
      - path: /api/v1/ai
        pathType: Prefix
        backend:
          service:
            name: ai-analysis-service
            port:
              number: 8000
EOF

    log_success "Ingress配置完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待所有服务就绪..."
    
    # 等待所有部署就绪
    kubectl wait --for=condition=available deployment --all -n $UAT_NAMESPACE --timeout=600s
    
    log_success "所有服务已就绪"
}

# 健康检查
health_check() {
    log_info "执行UAT环境健康检查..."
    
    # 检查所有Pod状态
    kubectl get pods -n $UAT_NAMESPACE
    
    # 检查服务状态
    kubectl get services -n $UAT_NAMESPACE
    
    # 检查Ingress状态
    kubectl get ingress -n $UAT_NAMESPACE
    
    log_success "健康检查完成"
}

# 主函数
main() {
    log_info "开始部署AI项目管理平台UAT环境..."
    
    check_prerequisites
    create_namespace
    deploy_database
    wait_for_database
    initialize_database
    deploy_applications
    configure_ingress
    wait_for_services
    health_check
    
    log_success "UAT环境部署完成！"
    log_info "访问地址: https://$UAT_DOMAIN"
    log_info "API地址: https://uat-api.ai-pm-test.com"
    log_info "请等待DNS解析生效后访问系统"
}

# 清理函数
cleanup() {
    log_warning "清理UAT环境..."
    kubectl delete namespace $UAT_NAMESPACE --ignore-not-found=true
    log_success "UAT环境清理完成"
}

# 脚本参数处理
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "cleanup")
        cleanup
        ;;
    "health")
        health_check
        ;;
    *)
        echo "用法: $0 [deploy|cleanup|health]"
        echo "  deploy  - 部署UAT环境（默认）"
        echo "  cleanup - 清理UAT环境"
        echo "  health  - 健康检查"
        exit 1
        ;;
esac
