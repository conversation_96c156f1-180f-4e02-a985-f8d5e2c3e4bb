#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI项目管理平台安全渗透测试自动化脚本
版本: v1.0
创建时间: 2025-08-28
描述: 自动化安全测试，包括OWASP Top 10漏洞检测和常见安全问题扫描
"""

import os
import json
import time
import requests
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('security_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SecurityTestConfig:
    """安全测试配置类"""
    
    def __init__(self):
        self.base_url = os.getenv('TEST_BASE_URL', 'https://api.ai-pm.your-domain.com')
        self.frontend_url = os.getenv('FRONTEND_URL', 'https://ai-pm.your-domain.com')
        self.test_username = os.getenv('TEST_USERNAME', 'test_user_001')
        self.test_password = os.getenv('TEST_PASSWORD', 'Test123456!')
        
        # 测试配置
        self.max_threads = 10
        self.request_timeout = 30
        self.scan_delay = 0.5  # 请求间隔，避免过于激进
        
        # 漏洞检测配置
        self.sql_injection_payloads = self._load_sql_payloads()
        self.xss_payloads = self._load_xss_payloads()
        self.path_traversal_payloads = self._load_path_traversal_payloads()
        
        # 结果存储
        self.vulnerabilities = []
        self.scan_results = {}
    
    def _load_sql_payloads(self) -> List[str]:
        """加载SQL注入测试载荷"""
        return [
            "' OR '1'='1' --",
            "' OR '1'='1' /*",
            "' UNION SELECT NULL --",
            "' UNION SELECT 1,2,3 --",
            "admin'--",
            "admin'/*",
            "' OR 1=1 --",
            "' OR 'a'='a",
            "') OR ('1'='1' --",
            "1' AND '1'='1",
            "1' AND '1'='2",
            "' WAITFOR DELAY '00:00:05' --",
            "'; DROP TABLE users; --"
        ]
    
    def _load_xss_payloads(self) -> List[str]:
        """加载XSS测试载荷"""
        return [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>",
            "<keygen onfocus=alert('XSS') autofocus>",
            "<video><source onerror=alert('XSS')>",
            "<audio src=x onerror=alert('XSS')>"
        ]
    
    def _load_path_traversal_payloads(self) -> List[str]:
        """加载路径遍历测试载荷"""
        return [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "..%2F..%2F..%2Fetc%2Fpasswd",
            "..%252F..%252F..%252Fetc%252Fpasswd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd"
        ]

class SecurityScanner:
    """安全扫描器"""
    
    def __init__(self, config: SecurityTestConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = config.request_timeout
        self.session.headers.update({
            'User-Agent': 'AI-PM-Security-Scanner/1.0'
        })
        self.auth_token = None
    
    def authenticate(self) -> bool:
        """用户认证"""
        try:
            login_url = urljoin(self.config.base_url, '/api/v1/users/login')
            response = self.session.post(login_url, json={
                'username': self.config.test_username,
                'password': self.config.test_password
            })
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('token')
                if self.auth_token:
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.auth_token}'
                    })
                    logger.info("认证成功")
                    return True
            
            logger.error(f"认证失败: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"认证过程出错: {e}")
            return False
    
    def test_sql_injection(self, endpoints: List[str]) -> List[Dict]:
        """SQL注入测试"""
        logger.info("开始SQL注入测试...")
        vulnerabilities = []
        
        for endpoint in endpoints:
            for payload in self.config.sql_injection_payloads:
                try:
                    # 测试GET参数
                    test_url = f"{urljoin(self.config.base_url, endpoint)}?id={payload}"
                    response = self.session.get(test_url)
                    
                    # 检测SQL错误信息
                    if self._detect_sql_error(response.text):
                        vuln = {
                            'type': 'SQL Injection',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'payload': payload,
                            'method': 'GET',
                            'evidence': 'SQL error detected in response'
                        }
                        vulnerabilities.append(vuln)
                        logger.warning(f"发现SQL注入漏洞: {endpoint}")
                    
                    # 测试POST参数
                    if endpoint.endswith('/login') or endpoint.endswith('/search'):
                        post_data = {'username': payload, 'password': 'test'}
                        response = self.session.post(
                            urljoin(self.config.base_url, endpoint), 
                            json=post_data
                        )
                        
                        if self._detect_sql_error(response.text):
                            vuln = {
                                'type': 'SQL Injection',
                                'severity': 'High',
                                'endpoint': endpoint,
                                'payload': payload,
                                'method': 'POST',
                                'evidence': 'SQL error detected in response'
                            }
                            vulnerabilities.append(vuln)
                    
                    time.sleep(self.config.scan_delay)
                    
                except Exception as e:
                    logger.debug(f"SQL注入测试错误 {endpoint}: {e}")
        
        logger.info(f"SQL注入测试完成，发现 {len(vulnerabilities)} 个漏洞")
        return vulnerabilities
    
    def test_xss(self, endpoints: List[str]) -> List[Dict]:
        """XSS跨站脚本测试"""
        logger.info("开始XSS测试...")
        vulnerabilities = []
        
        for endpoint in endpoints:
            for payload in self.config.xss_payloads:
                try:
                    # 测试GET参数
                    test_url = f"{urljoin(self.config.base_url, endpoint)}?search={payload}"
                    response = self.session.get(test_url)
                    
                    # 检测XSS反射
                    if payload in response.text and 'script' in payload.lower():
                        vuln = {
                            'type': 'Cross-Site Scripting (XSS)',
                            'severity': 'Medium',
                            'endpoint': endpoint,
                            'payload': payload,
                            'method': 'GET',
                            'evidence': 'Payload reflected in response'
                        }
                        vulnerabilities.append(vuln)
                        logger.warning(f"发现XSS漏洞: {endpoint}")
                    
                    # 测试POST参数
                    if 'create' in endpoint or 'update' in endpoint:
                        post_data = {'title': payload, 'description': payload}
                        response = self.session.post(
                            urljoin(self.config.base_url, endpoint),
                            json=post_data
                        )
                        
                        if payload in response.text and 'script' in payload.lower():
                            vuln = {
                                'type': 'Cross-Site Scripting (XSS)',
                                'severity': 'Medium',
                                'endpoint': endpoint,
                                'payload': payload,
                                'method': 'POST',
                                'evidence': 'Payload reflected in response'
                            }
                            vulnerabilities.append(vuln)
                    
                    time.sleep(self.config.scan_delay)
                    
                except Exception as e:
                    logger.debug(f"XSS测试错误 {endpoint}: {e}")
        
        logger.info(f"XSS测试完成，发现 {len(vulnerabilities)} 个漏洞")
        return vulnerabilities
    
    def test_authentication_bypass(self) -> List[Dict]:
        """认证绕过测试"""
        logger.info("开始认证绕过测试...")
        vulnerabilities = []
        
        # 测试未认证访问受保护资源
        protected_endpoints = [
            '/api/v1/projects',
            '/api/v1/users/profile',
            '/api/v1/admin/users',
            '/api/v1/projects/1/tasks'
        ]
        
        # 临时移除认证头
        original_auth = self.session.headers.get('Authorization')
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
        
        for endpoint in protected_endpoints:
            try:
                response = self.session.get(urljoin(self.config.base_url, endpoint))
                
                # 如果返回200而不是401/403，可能存在认证绕过
                if response.status_code == 200:
                    vuln = {
                        'type': 'Authentication Bypass',
                        'severity': 'High',
                        'endpoint': endpoint,
                        'evidence': f'Accessed protected resource without authentication (HTTP {response.status_code})'
                    }
                    vulnerabilities.append(vuln)
                    logger.warning(f"发现认证绕过漏洞: {endpoint}")
                
                time.sleep(self.config.scan_delay)
                
            except Exception as e:
                logger.debug(f"认证绕过测试错误 {endpoint}: {e}")
        
        # 恢复认证头
        if original_auth:
            self.session.headers['Authorization'] = original_auth
        
        logger.info(f"认证绕过测试完成，发现 {len(vulnerabilities)} 个漏洞")
        return vulnerabilities
    
    def test_authorization_flaws(self) -> List[Dict]:
        """权限缺陷测试"""
        logger.info("开始权限缺陷测试...")
        vulnerabilities = []
        
        # 测试水平权限提升（访问其他用户数据）
        test_cases = [
            ('/api/v1/users/2/profile', 'Access other user profile'),
            ('/api/v1/projects/999', 'Access unauthorized project'),
            ('/api/v1/admin/users', 'Access admin endpoints')
        ]
        
        for endpoint, description in test_cases:
            try:
                response = self.session.get(urljoin(self.config.base_url, endpoint))
                
                # 检查是否能访问不应该访问的资源
                if response.status_code == 200:
                    vuln = {
                        'type': 'Authorization Flaw',
                        'severity': 'High',
                        'endpoint': endpoint,
                        'evidence': f'{description} - HTTP {response.status_code}'
                    }
                    vulnerabilities.append(vuln)
                    logger.warning(f"发现权限缺陷: {endpoint}")
                
                time.sleep(self.config.scan_delay)
                
            except Exception as e:
                logger.debug(f"权限缺陷测试错误 {endpoint}: {e}")
        
        logger.info(f"权限缺陷测试完成，发现 {len(vulnerabilities)} 个漏洞")
        return vulnerabilities
    
    def test_sensitive_data_exposure(self) -> List[Dict]:
        """敏感数据泄露测试"""
        logger.info("开始敏感数据泄露测试...")
        vulnerabilities = []
        
        # 测试常见的敏感信息泄露路径
        sensitive_paths = [
            '/.env',
            '/config.json',
            '/swagger.json',
            '/api/docs',
            '/debug',
            '/actuator/env',
            '/actuator/configprops',
            '/health',
            '/info'
        ]
        
        for path in sensitive_paths:
            try:
                response = self.session.get(urljoin(self.config.base_url, path))
                
                if response.status_code == 200:
                    # 检查响应中是否包含敏感信息
                    sensitive_keywords = [
                        'password', 'secret', 'key', 'token', 'database',
                        'connection', 'config', 'env', 'debug'
                    ]
                    
                    response_text = response.text.lower()
                    found_keywords = [kw for kw in sensitive_keywords if kw in response_text]
                    
                    if found_keywords:
                        vuln = {
                            'type': 'Sensitive Data Exposure',
                            'severity': 'Medium',
                            'endpoint': path,
                            'evidence': f'Sensitive keywords found: {", ".join(found_keywords)}'
                        }
                        vulnerabilities.append(vuln)
                        logger.warning(f"发现敏感数据泄露: {path}")
                
                time.sleep(self.config.scan_delay)
                
            except Exception as e:
                logger.debug(f"敏感数据泄露测试错误 {path}: {e}")
        
        logger.info(f"敏感数据泄露测试完成，发现 {len(vulnerabilities)} 个漏洞")
        return vulnerabilities
    
    def test_security_headers(self) -> List[Dict]:
        """安全头检测"""
        logger.info("开始安全头检测...")
        vulnerabilities = []
        
        try:
            response = self.session.get(self.config.frontend_url)
            headers = response.headers
            
            # 检查重要的安全头
            security_headers = {
                'X-Frame-Options': 'Missing X-Frame-Options header (Clickjacking protection)',
                'X-Content-Type-Options': 'Missing X-Content-Type-Options header',
                'X-XSS-Protection': 'Missing X-XSS-Protection header',
                'Strict-Transport-Security': 'Missing HSTS header',
                'Content-Security-Policy': 'Missing CSP header'
            }
            
            for header, description in security_headers.items():
                if header not in headers:
                    vuln = {
                        'type': 'Missing Security Header',
                        'severity': 'Low',
                        'endpoint': self.config.frontend_url,
                        'evidence': description
                    }
                    vulnerabilities.append(vuln)
                    logger.warning(f"缺少安全头: {header}")
            
        except Exception as e:
            logger.error(f"安全头检测错误: {e}")
        
        logger.info(f"安全头检测完成，发现 {len(vulnerabilities)} 个问题")
        return vulnerabilities
    
    def _detect_sql_error(self, response_text: str) -> bool:
        """检测SQL错误信息"""
        sql_errors = [
            'sql syntax', 'mysql_fetch', 'ora-', 'postgresql',
            'sqlite_', 'sqlstate', 'syntax error', 'mysql_num_rows',
            'pg_query', 'pg_exec', 'database error', 'sql error'
        ]
        
        response_lower = response_text.lower()
        return any(error in response_lower for error in sql_errors)

class SecurityTestRunner:
    """安全测试运行器"""
    
    def __init__(self):
        self.config = SecurityTestConfig()
        self.scanner = SecurityScanner(self.config)
        self.all_vulnerabilities = []
    
    def run_security_scan(self):
        """运行完整的安全扫描"""
        logger.info("开始安全渗透测试...")
        
        # 认证
        if not self.scanner.authenticate():
            logger.error("认证失败，无法继续安全测试")
            return
        
        # 定义测试端点
        test_endpoints = [
            '/api/v1/users/login',
            '/api/v1/users/register',
            '/api/v1/projects',
            '/api/v1/projects/1',
            '/api/v1/projects/1/tasks',
            '/api/v1/ai/projects/1/analysis'
        ]
        
        # 执行各种安全测试
        test_methods = [
            ('SQL注入测试', lambda: self.scanner.test_sql_injection(test_endpoints)),
            ('XSS测试', lambda: self.scanner.test_xss(test_endpoints)),
            ('认证绕过测试', lambda: self.scanner.test_authentication_bypass()),
            ('权限缺陷测试', lambda: self.scanner.test_authorization_flaws()),
            ('敏感数据泄露测试', lambda: self.scanner.test_sensitive_data_exposure()),
            ('安全头检测', lambda: self.scanner.test_security_headers())
        ]
        
        for test_name, test_method in test_methods:
            try:
                logger.info(f"执行 {test_name}...")
                vulnerabilities = test_method()
                self.all_vulnerabilities.extend(vulnerabilities)
                time.sleep(1)  # 测试间隔
            except Exception as e:
                logger.error(f"{test_name} 执行失败: {e}")
        
        # 生成报告
        self._generate_security_report()
        
        logger.info("安全渗透测试完成")
    
    def _generate_security_report(self):
        """生成安全测试报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 统计漏洞
        severity_count = {'High': 0, 'Medium': 0, 'Low': 0}
        for vuln in self.all_vulnerabilities:
            severity_count[vuln['severity']] += 1
        
        # 生成JSON报告
        report_data = {
            'scan_info': {
                'timestamp': timestamp,
                'target': self.config.base_url,
                'total_vulnerabilities': len(self.all_vulnerabilities)
            },
            'summary': severity_count,
            'vulnerabilities': self.all_vulnerabilities
        }
        
        json_file = f'security_scan_results_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 生成Markdown报告
        report_md = f"""# 安全渗透测试报告

## 扫描概要
- 扫描时间: {timestamp}
- 目标系统: {self.config.base_url}
- 发现漏洞总数: {len(self.all_vulnerabilities)}

## 风险统计
| 严重程度 | 数量 | 修复优先级 |
|----------|------|------------|
| 高危 | {severity_count['High']} | 立即修复 |
| 中危 | {severity_count['Medium']} | 1周内修复 |
| 低危 | {severity_count['Low']} | 1月内修复 |

## 漏洞详情
"""
        
        for i, vuln in enumerate(self.all_vulnerabilities, 1):
            report_md += f"""
### {i}. {vuln['type']}
- **严重程度**: {vuln['severity']}
- **端点**: {vuln['endpoint']}
- **证据**: {vuln['evidence']}
"""
            if 'payload' in vuln:
                report_md += f"- **测试载荷**: `{vuln['payload']}`\n"
        
        report_md += f"""
## 修复建议
1. **高危漏洞**: 立即修复，这些漏洞可能导致系统被完全攻破
2. **中危漏洞**: 1周内修复，这些漏洞可能导致数据泄露或权限提升
3. **低危漏洞**: 1月内修复，这些漏洞主要影响安全配置

## 安全加固建议
- 实施输入验证和输出编码
- 加强身份认证和授权机制
- 配置安全HTTP头
- 定期进行安全扫描和渗透测试
- 建立安全事件响应流程

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        md_file = f'security_test_report_{timestamp}.md'
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(report_md)
        
        logger.info(f"安全测试报告已生成:")
        logger.info(f"  JSON报告: {json_file}")
        logger.info(f"  Markdown报告: {md_file}")
        
        # 输出摘要
        if self.all_vulnerabilities:
            logger.warning(f"发现 {len(self.all_vulnerabilities)} 个安全问题:")
            logger.warning(f"  高危: {severity_count['High']} 个")
            logger.warning(f"  中危: {severity_count['Medium']} 个")
            logger.warning(f"  低危: {severity_count['Low']} 个")
        else:
            logger.info("未发现明显的安全漏洞")

def main():
    """主函数"""
    logger.info("AI项目管理平台安全渗透测试开始")
    
    # 创建测试运行器
    runner = SecurityTestRunner()
    
    # 执行安全扫描
    runner.run_security_scan()
    
    logger.info("安全渗透测试完成")

if __name__ == "__main__":
    main()
