#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI项目管理平台性能测试自动化脚本
版本: v1.0
创建时间: 2025-08-28
描述: 使用Locust进行性能压力测试，支持多种测试场景和实时监控
"""

import os
import time
import json
import random
import logging
from datetime import datetime
from typing import Dict, List, Any
from locust import HttpUser, task, between, events
from locust.env import Environment
from locust.stats import stats_printer, stats_history
from locust.log import setup_logging
import requests
import psutil
import threading

# 配置日志
setup_logging("INFO", None)
logger = logging.getLogger(__name__)

class PerformanceTestConfig:
    """性能测试配置类"""
    
    def __init__(self):
        self.base_url = os.getenv('TEST_BASE_URL', 'https://api.ai-pm.your-domain.com')
        self.test_users_count = int(os.getenv('TEST_USERS', '1000'))
        self.test_duration = int(os.getenv('TEST_DURATION', '3600'))  # 秒
        self.ramp_up_time = int(os.getenv('RAMP_UP_TIME', '300'))  # 秒
        
        # 测试数据配置
        self.test_users_data = self._load_test_users()
        self.test_projects_data = self._load_test_projects()
        
        # 性能阈值配置
        self.response_time_threshold = 200  # ms
        self.error_rate_threshold = 1.0  # %
        self.cpu_threshold = 80.0  # %
        self.memory_threshold = 85.0  # %
    
    def _load_test_users(self) -> List[Dict]:
        """加载测试用户数据"""
        users = []
        for i in range(1, 1001):
            users.append({
                'username': f'test_user_{i:03d}',
                'password': 'Test123456!',
                'email': f'user{i:03d}@uat-test.com'
            })
        return users
    
    def _load_test_projects(self) -> List[Dict]:
        """加载测试项目数据"""
        projects = []
        project_names = [
            '电商平台开发', '移动办公APP', '数据分析平台', 
            'AI智能客服', '企业官网重构', '微服务架构升级',
            '用户体验优化', '性能监控系统', '安全防护升级', '云原生迁移'
        ]
        
        for i, name in enumerate(project_names * 10):  # 100个项目
            projects.append({
                'id': i + 1,
                'name': f'{name}_{i+1:03d}',
                'description': f'这是{name}项目的详细描述，用于性能测试。'
            })
        return projects

class AIProjectManagementUser(HttpUser):
    """AI项目管理平台用户行为模拟"""
    
    wait_time = between(1, 5)  # 用户操作间隔时间
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.config = PerformanceTestConfig()
        self.token = None
        self.user_data = None
        self.current_project_id = None
    
    def on_start(self):
        """用户会话开始时的初始化"""
        self.user_data = random.choice(self.config.test_users_data)
        self.login()
    
    def on_stop(self):
        """用户会话结束时的清理"""
        if self.token:
            self.logout()
    
    @task(1)
    def login(self):
        """用户登录"""
        with self.client.post(
            "/api/v1/users/login",
            json={
                "username": self.user_data['username'],
                "password": self.user_data['password']
            },
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('token')
                if self.token:
                    self.client.headers.update({
                        "Authorization": f"Bearer {self.token}"
                    })
                    response.success()
                    logger.info(f"用户 {self.user_data['username']} 登录成功")
                else:
                    response.failure("登录响应中缺少token")
            else:
                response.failure(f"登录失败: {response.status_code}")
    
    @task(5)
    def view_projects_list(self):
        """查看项目列表"""
        with self.client.get(
            "/api/v1/projects",
            params={"page": 1, "size": 20},
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and len(data['data']) > 0:
                    # 随机选择一个项目用于后续操作
                    self.current_project_id = random.choice(data['data'])['id']
                    response.success()
                else:
                    response.failure("项目列表为空")
            else:
                response.failure(f"获取项目列表失败: {response.status_code}")
    
    @task(3)
    def view_project_detail(self):
        """查看项目详情"""
        if not self.current_project_id:
            self.current_project_id = random.randint(1, 100)
        
        with self.client.get(
            f"/api/v1/projects/{self.current_project_id}",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 404:
                response.success()  # 项目不存在也是正常情况
            else:
                response.failure(f"获取项目详情失败: {response.status_code}")
    
    @task(4)
    def view_tasks_list(self):
        """查看任务列表"""
        if not self.current_project_id:
            self.current_project_id = random.randint(1, 100)
        
        with self.client.get(
            f"/api/v1/projects/{self.current_project_id}/tasks",
            params={"page": 1, "size": 20},
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:
                response.success()
            else:
                response.failure(f"获取任务列表失败: {response.status_code}")
    
    @task(2)
    def create_task(self):
        """创建任务"""
        if not self.current_project_id:
            self.current_project_id = random.randint(1, 100)
        
        task_data = {
            "title": f"性能测试任务_{random.randint(1, 10000)}",
            "description": "这是一个由性能测试自动创建的任务",
            "priority": random.choice(["LOW", "MEDIUM", "HIGH", "URGENT"]),
            "status": "TODO",
            "estimatedHours": random.randint(1, 40)
        }
        
        with self.client.post(
            f"/api/v1/projects/{self.current_project_id}/tasks",
            json=task_data,
            catch_response=True
        ) as response:
            if response.status_code in [200, 201]:
                response.success()
            else:
                response.failure(f"创建任务失败: {response.status_code}")
    
    @task(2)
    def update_task_status(self):
        """更新任务状态"""
        if not self.current_project_id:
            self.current_project_id = random.randint(1, 100)
        
        task_id = random.randint(1, 1000)
        new_status = random.choice(["TODO", "IN_PROGRESS", "TESTING", "DONE"])
        
        with self.client.put(
            f"/api/v1/projects/{self.current_project_id}/tasks/{task_id}",
            json={"status": new_status},
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:
                response.success()
            else:
                response.failure(f"更新任务状态失败: {response.status_code}")
    
    @task(1)
    def ai_analysis_progress(self):
        """AI进度分析"""
        if not self.current_project_id:
            self.current_project_id = random.randint(1, 100)
        
        with self.client.get(
            f"/api/v1/ai/projects/{self.current_project_id}/progress-prediction",
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:
                response.success()
            else:
                response.failure(f"AI进度分析失败: {response.status_code}")
    
    @task(1)
    def ai_analysis_risk(self):
        """AI风险分析"""
        if not self.current_project_id:
            self.current_project_id = random.randint(1, 100)
        
        with self.client.get(
            f"/api/v1/ai/projects/{self.current_project_id}/risk-analysis",
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:
                response.success()
            else:
                response.failure(f"AI风险分析失败: {response.status_code}")
    
    def logout(self):
        """用户登出"""
        if self.token:
            with self.client.post(
                "/api/v1/users/logout",
                catch_response=True
            ) as response:
                if response.status_code in [200, 401]:
                    response.success()
                    logger.info(f"用户 {self.user_data['username']} 登出成功")
                else:
                    response.failure(f"登出失败: {response.status_code}")

class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics_data = []
        self.config = PerformanceTestConfig()
    
    def start_monitoring(self):
        """开始性能监控"""
        self.monitoring = True
        monitor_thread = threading.Thread(target=self._monitor_system)
        monitor_thread.daemon = True
        monitor_thread.start()
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        logger.info("性能监控已停止")
    
    def _monitor_system(self):
        """系统资源监控"""
        while self.monitoring:
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                # 记录监控数据
                metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / (1024**3),
                    'disk_percent': disk.percent,
                    'disk_used_gb': disk.used / (1024**3)
                }
                
                self.metrics_data.append(metrics)
                
                # 检查性能阈值
                if cpu_percent > self.config.cpu_threshold:
                    logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
                
                if memory.percent > self.config.memory_threshold:
                    logger.warning(f"内存使用率过高: {memory.percent:.1f}%")
                
                time.sleep(10)  # 每10秒监控一次
                
            except Exception as e:
                logger.error(f"系统监控错误: {e}")
                time.sleep(5)
    
    def save_metrics(self, filename: str):
        """保存监控数据"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.metrics_data, f, indent=2, ensure_ascii=False)
            logger.info(f"监控数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存监控数据失败: {e}")

class PerformanceTestRunner:
    """性能测试运行器"""
    
    def __init__(self):
        self.config = PerformanceTestConfig()
        self.monitor = PerformanceMonitor()
        self.test_results = {}
    
    def run_load_test(self):
        """运行负载测试"""
        logger.info("开始执行负载测试...")
        
        # 启动性能监控
        self.monitor.start_monitoring()
        
        try:
            # 配置Locust环境
            env = Environment(user_classes=[AIProjectManagementUser])
            
            # 启动测试
            env.create_local_runner()
            env.runner.start(
                user_count=self.config.test_users_count,
                spawn_rate=self.config.test_users_count / self.config.ramp_up_time
            )
            
            # 运行测试
            logger.info(f"测试运行中，目标用户数: {self.config.test_users_count}")
            logger.info(f"测试持续时间: {self.config.test_duration}秒")
            
            time.sleep(self.config.test_duration)
            
            # 停止测试
            env.runner.stop()
            
            # 收集测试结果
            self._collect_results(env)
            
        except Exception as e:
            logger.error(f"负载测试执行失败: {e}")
        finally:
            # 停止监控
            self.monitor.stop_monitoring()
            
            # 保存结果
            self._save_results()
    
    def _collect_results(self, env):
        """收集测试结果"""
        stats = env.runner.stats
        
        self.test_results = {
            'test_config': {
                'users': self.config.test_users_count,
                'duration': self.config.test_duration,
                'base_url': self.config.base_url
            },
            'summary': {
                'total_requests': stats.total.num_requests,
                'total_failures': stats.total.num_failures,
                'average_response_time': stats.total.avg_response_time,
                'min_response_time': stats.total.min_response_time,
                'max_response_time': stats.total.max_response_time,
                'requests_per_second': stats.total.current_rps,
                'failure_rate': stats.total.fail_ratio * 100
            },
            'endpoints': {}
        }
        
        # 收集各端点的详细统计
        for name, stat in stats.entries.items():
            if stat.num_requests > 0:
                self.test_results['endpoints'][name] = {
                    'requests': stat.num_requests,
                    'failures': stat.num_failures,
                    'avg_response_time': stat.avg_response_time,
                    'min_response_time': stat.min_response_time,
                    'max_response_time': stat.max_response_time,
                    'rps': stat.current_rps,
                    'failure_rate': stat.fail_ratio * 100
                }
        
        logger.info("测试结果收集完成")
    
    def _save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存测试结果
        results_file = f'performance_test_results_{timestamp}.json'
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            logger.info(f"测试结果已保存到: {results_file}")
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")
        
        # 保存监控数据
        metrics_file = f'system_metrics_{timestamp}.json'
        self.monitor.save_metrics(metrics_file)
        
        # 生成测试报告
        self._generate_report(timestamp)
    
    def _generate_report(self, timestamp: str):
        """生成测试报告"""
        if not self.test_results:
            return
        
        summary = self.test_results['summary']
        
        # 判断测试是否通过
        passed = (
            summary['average_response_time'] <= self.config.response_time_threshold and
            summary['failure_rate'] <= self.config.error_rate_threshold
        )
        
        report = f"""
# 性能测试报告

## 测试概要
- 测试时间: {timestamp}
- 并发用户数: {self.config.test_users_count}
- 测试持续时间: {self.config.test_duration}秒
- 测试结果: {'✅ 通过' if passed else '❌ 失败'}

## 关键指标
- 总请求数: {summary['total_requests']:,}
- 失败请求数: {summary['total_failures']:,}
- 平均响应时间: {summary['average_response_time']:.1f}ms
- 最大响应时间: {summary['max_response_time']:.1f}ms
- 每秒请求数: {summary['requests_per_second']:.1f}
- 错误率: {summary['failure_rate']:.2f}%

## 性能评估
- 响应时间要求 (< {self.config.response_time_threshold}ms): {'✅' if summary['average_response_time'] <= self.config.response_time_threshold else '❌'}
- 错误率要求 (< {self.config.error_rate_threshold}%): {'✅' if summary['failure_rate'] <= self.config.error_rate_threshold else '❌'}

## 详细数据
详细的测试数据请查看: performance_test_results_{timestamp}.json
系统监控数据请查看: system_metrics_{timestamp}.json
"""
        
        report_file = f'performance_test_report_{timestamp}.md'
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"测试报告已生成: {report_file}")
        except Exception as e:
            logger.error(f"生成测试报告失败: {e}")

def main():
    """主函数"""
    logger.info("AI项目管理平台性能测试开始")
    
    # 创建测试运行器
    runner = PerformanceTestRunner()
    
    # 执行负载测试
    runner.run_load_test()
    
    logger.info("性能测试完成")

if __name__ == "__main__":
    main()
