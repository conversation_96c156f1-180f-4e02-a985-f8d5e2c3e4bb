#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI项目管理平台监控体系验证脚本
版本: v1.0
创建时间: 2025-08-28
描述: 自动化验证Prometheus监控指标、Grafana仪表板和告警规则
"""

import os
import json
import time
import requests
import logging
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import yaml
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('monitoring_verification.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MonitoringConfig:
    """监控配置类"""
    
    def __init__(self):
        self.prometheus_url = os.getenv('PROMETHEUS_URL', 'http://prometheus.ai-pm-prod.svc.cluster.local:9090')
        self.grafana_url = os.getenv('GRAFANA_URL', 'http://grafana.ai-pm-prod.svc.cluster.local:3000')
        self.alertmanager_url = os.getenv('ALERTMANAGER_URL', 'http://alertmanager.ai-pm-prod.svc.cluster.local:9093')
        
        # 认证信息
        self.grafana_username = os.getenv('GRAFANA_USERNAME', 'admin')
        self.grafana_password = os.getenv('GRAFANA_PASSWORD', 'admin123')
        
        # 验证配置
        self.verification_timeout = 300  # 5分钟
        self.metric_check_interval = 30  # 30秒
        self.alert_check_interval = 60   # 1分钟
        
        # 验证结果
        self.verification_results = {
            'prometheus_metrics': {},
            'grafana_dashboards': {},
            'alert_rules': {},
            'notification_channels': {},
            'overall_status': 'UNKNOWN'
        }

class PrometheusVerifier:
    """Prometheus监控验证器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = 30
    
    def verify_prometheus_connectivity(self) -> bool:
        """验证Prometheus连接性"""
        try:
            response = self.session.get(f"{self.config.prometheus_url}/api/v1/status/config")
            if response.status_code == 200:
                logger.info("Prometheus连接验证成功")
                return True
            else:
                logger.error(f"Prometheus连接失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Prometheus连接异常: {e}")
            return False
    
    def verify_metrics_collection(self) -> Dict[str, Any]:
        """验证指标收集"""
        logger.info("开始验证Prometheus指标收集...")
        
        # 定义需要验证的关键指标
        key_metrics = {
            'node_cpu_seconds_total': '节点CPU使用率',
            'node_memory_MemAvailable_bytes': '节点内存可用量',
            'node_filesystem_avail_bytes': '节点磁盘可用空间',
            'kube_pod_status_ready': 'Pod就绪状态',
            'http_server_requests_seconds': 'HTTP请求指标',
            'jvm_memory_used_bytes': 'JVM内存使用',
            'pg_up': 'PostgreSQL状态',
            'redis_up': 'Redis状态'
        }
        
        results = {}
        
        for metric, description in key_metrics.items():
            try:
                # 查询指标
                query_url = f"{self.config.prometheus_url}/api/v1/query"
                params = {'query': metric}
                response = self.session.get(query_url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    if data['status'] == 'success' and data['data']['result']:
                        results[metric] = {
                            'status': 'SUCCESS',
                            'description': description,
                            'sample_count': len(data['data']['result']),
                            'last_value': data['data']['result'][0]['value'][1] if data['data']['result'] else None
                        }
                        logger.info(f"指标 {metric} 验证成功，样本数: {len(data['data']['result'])}")
                    else:
                        results[metric] = {
                            'status': 'NO_DATA',
                            'description': description,
                            'error': '无数据返回'
                        }
                        logger.warning(f"指标 {metric} 无数据返回")
                else:
                    results[metric] = {
                        'status': 'ERROR',
                        'description': description,
                        'error': f'HTTP {response.status_code}'
                    }
                    logger.error(f"指标 {metric} 查询失败: HTTP {response.status_code}")
                    
            except Exception as e:
                results[metric] = {
                    'status': 'EXCEPTION',
                    'description': description,
                    'error': str(e)
                }
                logger.error(f"指标 {metric} 查询异常: {e}")
        
        return results
    
    def verify_alert_rules(self) -> Dict[str, Any]:
        """验证告警规则"""
        logger.info("开始验证Prometheus告警规则...")
        
        try:
            # 获取告警规则
            rules_url = f"{self.config.prometheus_url}/api/v1/rules"
            response = self.session.get(rules_url)
            
            if response.status_code != 200:
                return {'status': 'ERROR', 'error': f'HTTP {response.status_code}'}
            
            data = response.json()
            if data['status'] != 'success':
                return {'status': 'ERROR', 'error': 'API返回失败状态'}
            
            # 分析告警规则
            rule_groups = data['data']['groups']
            results = {
                'status': 'SUCCESS',
                'total_groups': len(rule_groups),
                'total_rules': 0,
                'active_alerts': 0,
                'groups': {}
            }
            
            for group in rule_groups:
                group_name = group['name']
                group_rules = group['rules']
                
                alert_rules = [rule for rule in group_rules if rule['type'] == 'alerting']
                recording_rules = [rule for rule in group_rules if rule['type'] == 'recording']
                
                # 统计活跃告警
                active_alerts = 0
                for rule in alert_rules:
                    if 'alerts' in rule and rule['alerts']:
                        active_alerts += len(rule['alerts'])
                
                results['groups'][group_name] = {
                    'alert_rules': len(alert_rules),
                    'recording_rules': len(recording_rules),
                    'active_alerts': active_alerts,
                    'evaluation_time': group.get('evaluationTime', 0)
                }
                
                results['total_rules'] += len(group_rules)
                results['active_alerts'] += active_alerts
            
            logger.info(f"告警规则验证完成: {results['total_groups']}个组，{results['total_rules']}条规则，{results['active_alerts']}个活跃告警")
            return results
            
        except Exception as e:
            logger.error(f"告警规则验证异常: {e}")
            return {'status': 'EXCEPTION', 'error': str(e)}
    
    def verify_targets_health(self) -> Dict[str, Any]:
        """验证监控目标健康状态"""
        logger.info("开始验证监控目标健康状态...")
        
        try:
            # 获取监控目标
            targets_url = f"{self.config.prometheus_url}/api/v1/targets"
            response = self.session.get(targets_url)
            
            if response.status_code != 200:
                return {'status': 'ERROR', 'error': f'HTTP {response.status_code}'}
            
            data = response.json()
            if data['status'] != 'success':
                return {'status': 'ERROR', 'error': 'API返回失败状态'}
            
            # 分析目标状态
            targets = data['data']['activeTargets']
            results = {
                'status': 'SUCCESS',
                'total_targets': len(targets),
                'healthy_targets': 0,
                'unhealthy_targets': 0,
                'targets_by_job': {},
                'unhealthy_details': []
            }
            
            for target in targets:
                job = target['labels']['job']
                health = target['health']
                
                if job not in results['targets_by_job']:
                    results['targets_by_job'][job] = {'healthy': 0, 'unhealthy': 0}
                
                if health == 'up':
                    results['healthy_targets'] += 1
                    results['targets_by_job'][job]['healthy'] += 1
                else:
                    results['unhealthy_targets'] += 1
                    results['targets_by_job'][job]['unhealthy'] += 1
                    results['unhealthy_details'].append({
                        'job': job,
                        'instance': target['labels']['instance'],
                        'health': health,
                        'last_error': target.get('lastError', ''),
                        'last_scrape': target.get('lastScrape', '')
                    })
            
            health_rate = (results['healthy_targets'] / results['total_targets']) * 100 if results['total_targets'] > 0 else 0
            logger.info(f"监控目标验证完成: {results['total_targets']}个目标，健康率: {health_rate:.1f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"监控目标验证异常: {e}")
            return {'status': 'EXCEPTION', 'error': str(e)}

class GrafanaVerifier:
    """Grafana仪表板验证器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = 30
        self.session.auth = (config.grafana_username, config.grafana_password)
    
    def verify_grafana_connectivity(self) -> bool:
        """验证Grafana连接性"""
        try:
            response = self.session.get(f"{self.config.grafana_url}/api/health")
            if response.status_code == 200:
                logger.info("Grafana连接验证成功")
                return True
            else:
                logger.error(f"Grafana连接失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Grafana连接异常: {e}")
            return False
    
    def verify_dashboards(self) -> Dict[str, Any]:
        """验证仪表板"""
        logger.info("开始验证Grafana仪表板...")
        
        try:
            # 获取仪表板列表
            search_url = f"{self.config.grafana_url}/api/search"
            response = self.session.get(search_url, params={'type': 'dash-db'})
            
            if response.status_code != 200:
                return {'status': 'ERROR', 'error': f'HTTP {response.status_code}'}
            
            dashboards = response.json()
            results = {
                'status': 'SUCCESS',
                'total_dashboards': len(dashboards),
                'dashboards': {}
            }
            
            # 验证每个仪表板
            for dashboard in dashboards:
                dashboard_uid = dashboard['uid']
                dashboard_title = dashboard['title']
                
                # 获取仪表板详情
                dashboard_url = f"{self.config.grafana_url}/api/dashboards/uid/{dashboard_uid}"
                detail_response = self.session.get(dashboard_url)
                
                if detail_response.status_code == 200:
                    dashboard_data = detail_response.json()
                    panels = dashboard_data['dashboard'].get('panels', [])
                    
                    results['dashboards'][dashboard_title] = {
                        'uid': dashboard_uid,
                        'status': 'SUCCESS',
                        'panels_count': len(panels),
                        'tags': dashboard.get('tags', []),
                        'folder': dashboard.get('folderTitle', 'General')
                    }
                    
                    logger.info(f"仪表板 '{dashboard_title}' 验证成功，包含 {len(panels)} 个面板")
                else:
                    results['dashboards'][dashboard_title] = {
                        'uid': dashboard_uid,
                        'status': 'ERROR',
                        'error': f'HTTP {detail_response.status_code}'
                    }
                    logger.error(f"仪表板 '{dashboard_title}' 验证失败")
            
            return results
            
        except Exception as e:
            logger.error(f"仪表板验证异常: {e}")
            return {'status': 'EXCEPTION', 'error': str(e)}
    
    def verify_data_sources(self) -> Dict[str, Any]:
        """验证数据源"""
        logger.info("开始验证Grafana数据源...")
        
        try:
            # 获取数据源列表
            datasources_url = f"{self.config.grafana_url}/api/datasources"
            response = self.session.get(datasources_url)
            
            if response.status_code != 200:
                return {'status': 'ERROR', 'error': f'HTTP {response.status_code}'}
            
            datasources = response.json()
            results = {
                'status': 'SUCCESS',
                'total_datasources': len(datasources),
                'datasources': {}
            }
            
            # 验证每个数据源
            for datasource in datasources:
                ds_name = datasource['name']
                ds_type = datasource['type']
                ds_id = datasource['id']
                
                # 测试数据源连接
                test_url = f"{self.config.grafana_url}/api/datasources/{ds_id}/health"
                test_response = self.session.get(test_url)
                
                if test_response.status_code == 200:
                    test_data = test_response.json()
                    results['datasources'][ds_name] = {
                        'type': ds_type,
                        'status': 'SUCCESS' if test_data.get('status') == 'OK' else 'WARNING',
                        'message': test_data.get('message', ''),
                        'url': datasource.get('url', '')
                    }
                    logger.info(f"数据源 '{ds_name}' ({ds_type}) 验证成功")
                else:
                    results['datasources'][ds_name] = {
                        'type': ds_type,
                        'status': 'ERROR',
                        'error': f'HTTP {test_response.status_code}'
                    }
                    logger.error(f"数据源 '{ds_name}' 验证失败")
            
            return results
            
        except Exception as e:
            logger.error(f"数据源验证异常: {e}")
            return {'status': 'EXCEPTION', 'error': str(e)}

class AlertManagerVerifier:
    """AlertManager告警验证器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = 30
    
    def verify_alertmanager_connectivity(self) -> bool:
        """验证AlertManager连接性"""
        try:
            response = self.session.get(f"{self.config.alertmanager_url}/api/v1/status")
            if response.status_code == 200:
                logger.info("AlertManager连接验证成功")
                return True
            else:
                logger.error(f"AlertManager连接失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"AlertManager连接异常: {e}")
            return False
    
    def verify_alert_routing(self) -> Dict[str, Any]:
        """验证告警路由配置"""
        logger.info("开始验证AlertManager告警路由...")
        
        try:
            # 获取配置信息
            config_url = f"{self.config.alertmanager_url}/api/v1/status"
            response = self.session.get(config_url)
            
            if response.status_code != 200:
                return {'status': 'ERROR', 'error': f'HTTP {response.status_code}'}
            
            status_data = response.json()
            
            # 获取活跃告警
            alerts_url = f"{self.config.alertmanager_url}/api/v1/alerts"
            alerts_response = self.session.get(alerts_url)
            
            results = {
                'status': 'SUCCESS',
                'cluster_status': status_data.get('cluster', {}),
                'version_info': status_data.get('versionInfo', {}),
                'config_hash': status_data.get('config', {}).get('original', ''),
                'active_alerts': 0,
                'silenced_alerts': 0
            }
            
            if alerts_response.status_code == 200:
                alerts_data = alerts_response.json()
                for alert in alerts_data:
                    if alert.get('status', {}).get('state') == 'active':
                        results['active_alerts'] += 1
                    elif alert.get('status', {}).get('state') == 'suppressed':
                        results['silenced_alerts'] += 1
            
            logger.info(f"AlertManager验证完成: {results['active_alerts']}个活跃告警，{results['silenced_alerts']}个静默告警")
            return results
            
        except Exception as e:
            logger.error(f"AlertManager验证异常: {e}")
            return {'status': 'EXCEPTION', 'error': str(e)}

class MonitoringVerificationRunner:
    """监控验证运行器"""
    
    def __init__(self):
        self.config = MonitoringConfig()
        self.prometheus_verifier = PrometheusVerifier(self.config)
        self.grafana_verifier = GrafanaVerifier(self.config)
        self.alertmanager_verifier = AlertManagerVerifier(self.config)
    
    def run_comprehensive_verification(self):
        """运行全面的监控验证"""
        logger.info("开始AI项目管理平台监控体系全面验证...")
        
        verification_start_time = datetime.now()
        
        # 1. 验证基础连接性
        logger.info("=== 第一阶段: 基础连接性验证 ===")
        connectivity_results = self._verify_connectivity()
        
        # 2. 验证Prometheus监控
        logger.info("=== 第二阶段: Prometheus监控验证 ===")
        prometheus_results = self._verify_prometheus()
        
        # 3. 验证Grafana仪表板
        logger.info("=== 第三阶段: Grafana仪表板验证 ===")
        grafana_results = self._verify_grafana()
        
        # 4. 验证AlertManager告警
        logger.info("=== 第四阶段: AlertManager告警验证 ===")
        alertmanager_results = self._verify_alertmanager()
        
        # 5. 生成验证报告
        logger.info("=== 第五阶段: 生成验证报告 ===")
        verification_end_time = datetime.now()
        
        # 汇总结果
        overall_results = {
            'verification_info': {
                'start_time': verification_start_time.isoformat(),
                'end_time': verification_end_time.isoformat(),
                'duration_seconds': (verification_end_time - verification_start_time).total_seconds()
            },
            'connectivity': connectivity_results,
            'prometheus': prometheus_results,
            'grafana': grafana_results,
            'alertmanager': alertmanager_results,
            'overall_status': self._calculate_overall_status(
                connectivity_results, prometheus_results, grafana_results, alertmanager_results
            )
        }
        
        # 保存结果
        self._save_verification_results(overall_results)
        
        # 生成报告
        self._generate_verification_report(overall_results)
        
        logger.info("监控体系验证完成")
        return overall_results
    
    def _verify_connectivity(self) -> Dict[str, Any]:
        """验证基础连接性"""
        results = {}
        
        # Prometheus连接性
        results['prometheus'] = self.prometheus_verifier.verify_prometheus_connectivity()
        
        # Grafana连接性
        results['grafana'] = self.grafana_verifier.verify_grafana_connectivity()
        
        # AlertManager连接性
        results['alertmanager'] = self.alertmanager_verifier.verify_alertmanager_connectivity()
        
        return results
    
    def _verify_prometheus(self) -> Dict[str, Any]:
        """验证Prometheus监控"""
        results = {}
        
        # 指标收集验证
        results['metrics'] = self.prometheus_verifier.verify_metrics_collection()
        
        # 告警规则验证
        results['alert_rules'] = self.prometheus_verifier.verify_alert_rules()
        
        # 监控目标验证
        results['targets'] = self.prometheus_verifier.verify_targets_health()
        
        return results
    
    def _verify_grafana(self) -> Dict[str, Any]:
        """验证Grafana仪表板"""
        results = {}
        
        # 仪表板验证
        results['dashboards'] = self.grafana_verifier.verify_dashboards()
        
        # 数据源验证
        results['datasources'] = self.grafana_verifier.verify_data_sources()
        
        return results
    
    def _verify_alertmanager(self) -> Dict[str, Any]:
        """验证AlertManager告警"""
        results = {}
        
        # 告警路由验证
        results['routing'] = self.alertmanager_verifier.verify_alert_routing()
        
        return results
    
    def _calculate_overall_status(self, *results) -> str:
        """计算总体状态"""
        all_success = True
        has_warning = False
        
        for result_group in results:
            for key, value in result_group.items():
                if isinstance(value, dict):
                    status = value.get('status', 'UNKNOWN')
                    if status in ['ERROR', 'EXCEPTION']:
                        all_success = False
                    elif status == 'WARNING':
                        has_warning = True
                elif isinstance(value, bool) and not value:
                    all_success = False
        
        if all_success:
            return 'WARNING' if has_warning else 'SUCCESS'
        else:
            return 'ERROR'
    
    def _save_verification_results(self, results: Dict[str, Any]):
        """保存验证结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'monitoring_verification_results_{timestamp}.json'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"验证结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存验证结果失败: {e}")
    
    def _generate_verification_report(self, results: Dict[str, Any]):
        """生成验证报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 计算统计信息
        connectivity_status = all(results['connectivity'].values())
        prometheus_metrics_success = sum(1 for m in results['prometheus']['metrics'].values() if m.get('status') == 'SUCCESS')
        prometheus_metrics_total = len(results['prometheus']['metrics'])
        
        grafana_dashboards_success = sum(1 for d in results['grafana']['dashboards']['dashboards'].values() if d.get('status') == 'SUCCESS')
        grafana_dashboards_total = results['grafana']['dashboards'].get('total_dashboards', 0)
        
        report = f"""# AI项目管理平台监控体系验证报告

## 验证概要
- **验证时间**: {results['verification_info']['start_time']}
- **验证耗时**: {results['verification_info']['duration_seconds']:.1f}秒
- **总体状态**: {'✅ 通过' if results['overall_status'] == 'SUCCESS' else '⚠️ 警告' if results['overall_status'] == 'WARNING' else '❌ 失败'}

## 连接性验证
- **Prometheus**: {'✅ 正常' if results['connectivity']['prometheus'] else '❌ 异常'}
- **Grafana**: {'✅ 正常' if results['connectivity']['grafana'] else '❌ 异常'}
- **AlertManager**: {'✅ 正常' if results['connectivity']['alertmanager'] else '❌ 异常'}

## Prometheus监控验证
### 指标收集
- **验证指标数**: {prometheus_metrics_total}
- **成功指标数**: {prometheus_metrics_success}
- **成功率**: {(prometheus_metrics_success/prometheus_metrics_total*100):.1f}%

### 告警规则
- **规则组数**: {results['prometheus']['alert_rules'].get('total_groups', 0)}
- **规则总数**: {results['prometheus']['alert_rules'].get('total_rules', 0)}
- **活跃告警**: {results['prometheus']['alert_rules'].get('active_alerts', 0)}

### 监控目标
- **目标总数**: {results['prometheus']['targets'].get('total_targets', 0)}
- **健康目标**: {results['prometheus']['targets'].get('healthy_targets', 0)}
- **健康率**: {(results['prometheus']['targets'].get('healthy_targets', 0)/max(results['prometheus']['targets'].get('total_targets', 1), 1)*100):.1f}%

## Grafana仪表板验证
- **仪表板总数**: {grafana_dashboards_total}
- **验证成功数**: {grafana_dashboards_success}
- **成功率**: {(grafana_dashboards_success/max(grafana_dashboards_total, 1)*100):.1f}%
- **数据源数**: {results['grafana']['datasources'].get('total_datasources', 0)}

## AlertManager告警验证
- **活跃告警**: {results['alertmanager']['routing'].get('active_alerts', 0)}
- **静默告警**: {results['alertmanager']['routing'].get('silenced_alerts', 0)}

## 验证建议
"""
        
        # 添加建议
        if results['overall_status'] != 'SUCCESS':
            report += "### 需要关注的问题\n"
            
            # 检查连接性问题
            for service, status in results['connectivity'].items():
                if not status:
                    report += f"- {service} 连接异常，请检查服务状态和网络配置\n"
            
            # 检查指标问题
            for metric, info in results['prometheus']['metrics'].items():
                if info.get('status') != 'SUCCESS':
                    report += f"- 指标 {metric} 收集异常: {info.get('error', '未知错误')}\n"
        
        report += f"""
---
**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**验证工具版本**: v1.0  
**负责团队**: 运维监控团队
"""
        
        # 保存报告
        report_filename = f'monitoring_verification_report_{timestamp}.md'
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"验证报告已生成: {report_filename}")
        except Exception as e:
            logger.error(f"生成验证报告失败: {e}")

def main():
    """主函数"""
    logger.info("AI项目管理平台监控体系验证开始")
    
    # 创建验证运行器
    runner = MonitoringVerificationRunner()
    
    # 执行全面验证
    results = runner.run_comprehensive_verification()
    
    # 输出验证结果摘要
    logger.info(f"验证完成，总体状态: {results['overall_status']}")
    
    return results

if __name__ == "__main__":
    main()
