#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI项目管理平台UAT测试数据准备脚本
版本: v1.0
创建时间: 2025-08-28
描述: 自动生成UAT测试所需的用户、项目、任务等测试数据
"""

import json
import uuid
import random
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any
import psycopg2
from psycopg2.extras import RealDictCursor
import redis
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uat_data_setup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UATDataGenerator:
    """UAT测试数据生成器"""
    
    def __init__(self, db_config: Dict[str, str], redis_config: Dict[str, str]):
        """
        初始化数据生成器
        
        Args:
            db_config: 数据库配置
            redis_config: Redis配置
        """
        self.db_config = db_config
        self.redis_config = redis_config
        self.db_conn = None
        self.redis_conn = None
        
        # 测试数据配置
        self.test_users = []
        self.test_projects = []
        self.test_tasks = []
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.db_conn = psycopg2.connect(**self.db_config)
            self.redis_conn = redis.Redis(**self.redis_config)
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.db_conn:
            self.db_conn.close()
        if self.redis_conn:
            self.redis_conn.close()
        logger.info("数据库连接已关闭")
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_test_users(self) -> List[Dict[str, Any]]:
        """生成测试用户数据"""
        logger.info("生成测试用户数据...")
        
        # 预定义的测试用户
        predefined_users = [
            {
                "username": "admin_uat",
                "email": "<EMAIL>",
                "password": "Admin123456!",
                "role": "ADMIN",
                "display_name": "UAT管理员"
            },
            {
                "username": "pm_zhang",
                "email": "<EMAIL>", 
                "password": "PM123456!",
                "role": "PROJECT_MANAGER",
                "display_name": "张项目经理"
            },
            {
                "username": "pm_li",
                "email": "<EMAIL>",
                "password": "PM123456!",
                "role": "PROJECT_MANAGER", 
                "display_name": "李项目经理"
            },
            {
                "username": "dev_wang",
                "email": "<EMAIL>",
                "password": "Dev123456!",
                "role": "DEVELOPER",
                "display_name": "王开发工程师"
            },
            {
                "username": "dev_chen",
                "email": "<EMAIL>",
                "password": "Dev123456!",
                "role": "DEVELOPER",
                "display_name": "陈开发工程师"
            },
            {
                "username": "qa_liu",
                "email": "<EMAIL>",
                "password": "QA123456!",
                "role": "TESTER",
                "display_name": "刘测试工程师"
            }
        ]
        
        # 生成额外的测试用户
        roles = ["DEVELOPER", "TESTER", "DESIGNER", "USER"]
        for i in range(7, 21):  # 生成14个额外用户
            user = {
                "username": f"test_user_{i:02d}",
                "email": f"user{i:02d}@uat-test.com",
                "password": "Test123456!",
                "role": random.choice(roles),
                "display_name": f"测试用户{i:02d}"
            }
            predefined_users.append(user)
        
        # 为每个用户添加ID和时间戳
        for user in predefined_users:
            user["id"] = str(uuid.uuid4())
            user["password_hash"] = self.hash_password(user["password"])
            user["created_at"] = datetime.now()
            user["updated_at"] = datetime.now()
        
        self.test_users = predefined_users
        logger.info(f"生成了 {len(predefined_users)} 个测试用户")
        return predefined_users
    
    def generate_test_projects(self) -> List[Dict[str, Any]]:
        """生成测试项目数据"""
        logger.info("生成测试项目数据...")
        
        project_templates = [
            {
                "name": "电商平台开发项目",
                "description": "基于微服务架构的电商平台开发，包含用户管理、商品管理、订单处理等核心功能模块。",
                "status": "ACTIVE",
                "project_type": "SOFTWARE_DEVELOPMENT"
            },
            {
                "name": "移动办公APP项目", 
                "description": "企业移动办公应用开发，支持考勤打卡、审批流程、即时通讯等功能。",
                "status": "ACTIVE",
                "project_type": "MOBILE_APP"
            },
            {
                "name": "数据分析平台项目",
                "description": "大数据分析平台建设，包含数据采集、清洗、分析和可视化展示功能。",
                "status": "PLANNING",
                "project_type": "DATA_ANALYTICS"
            },
            {
                "name": "AI智能客服系统",
                "description": "基于自然语言处理的智能客服系统，支持多轮对话和知识库问答。",
                "status": "ACTIVE",
                "project_type": "AI_PROJECT"
            },
            {
                "name": "企业官网重构项目",
                "description": "企业官方网站重新设计和开发，采用响应式设计，提升用户体验。",
                "status": "COMPLETED",
                "project_type": "WEB_DEVELOPMENT"
            }
        ]
        
        # 为每个项目分配项目经理
        pm_users = [user for user in self.test_users if user["role"] == "PROJECT_MANAGER"]
        
        projects = []
        for i, template in enumerate(project_templates):
            project = template.copy()
            project["id"] = str(uuid.uuid4())
            project["owner_id"] = pm_users[i % len(pm_users)]["id"]
            project["start_date"] = datetime.now() - timedelta(days=random.randint(30, 180))
            project["end_date"] = project["start_date"] + timedelta(days=random.randint(60, 365))
            project["created_at"] = datetime.now()
            project["updated_at"] = datetime.now()
            projects.append(project)
        
        self.test_projects = projects
        logger.info(f"生成了 {len(projects)} 个测试项目")
        return projects
    
    def generate_test_tasks(self) -> List[Dict[str, Any]]:
        """生成测试任务数据"""
        logger.info("生成测试任务数据...")
        
        task_templates = [
            "需求分析和设计",
            "数据库设计",
            "API接口开发",
            "前端页面开发",
            "单元测试编写",
            "集成测试",
            "性能优化",
            "安全测试",
            "用户文档编写",
            "部署配置"
        ]
        
        statuses = ["TODO", "IN_PROGRESS", "TESTING", "DONE", "CANCELLED"]
        priorities = ["LOW", "MEDIUM", "HIGH", "URGENT"]
        
        # 获取开发人员和测试人员
        dev_users = [user for user in self.test_users 
                    if user["role"] in ["DEVELOPER", "TESTER", "DESIGNER"]]
        
        tasks = []
        for project in self.test_projects:
            # 为每个项目生成10-20个任务
            task_count = random.randint(10, 20)
            
            for i in range(task_count):
                task = {
                    "id": str(uuid.uuid4()),
                    "title": f"{random.choice(task_templates)} - {project['name'][:10]}",
                    "description": f"项目 {project['name']} 中的 {random.choice(task_templates)} 任务，需要按照项目计划完成相关工作。",
                    "project_id": project["id"],
                    "assignee_id": random.choice(dev_users)["id"],
                    "status": random.choice(statuses),
                    "priority": random.choice(priorities),
                    "estimated_hours": random.randint(4, 40),
                    "actual_hours": random.randint(0, 50) if random.random() > 0.3 else None,
                    "due_date": datetime.now() + timedelta(days=random.randint(1, 60)),
                    "created_at": datetime.now() - timedelta(days=random.randint(1, 30)),
                    "updated_at": datetime.now()
                }
                tasks.append(task)
        
        self.test_tasks = tasks
        logger.info(f"生成了 {len(tasks)} 个测试任务")
        return tasks
    
    def insert_users(self):
        """插入用户数据"""
        logger.info("插入用户测试数据...")
        
        cursor = self.db_conn.cursor()
        
        # 清理现有测试数据
        cursor.execute("DELETE FROM users WHERE email LIKE '%uat-test.com'")
        
        # 插入用户数据
        insert_sql = """
        INSERT INTO users (id, username, email, password_hash, role, display_name, created_at, updated_at)
        VALUES (%(id)s, %(username)s, %(email)s, %(password_hash)s, %(role)s, %(display_name)s, %(created_at)s, %(updated_at)s)
        """
        
        for user in self.test_users:
            cursor.execute(insert_sql, user)
        
        self.db_conn.commit()
        logger.info(f"成功插入 {len(self.test_users)} 个用户")
    
    def insert_projects(self):
        """插入项目数据"""
        logger.info("插入项目测试数据...")
        
        cursor = self.db_conn.cursor()
        
        # 插入项目数据
        insert_sql = """
        INSERT INTO projects (id, name, description, owner_id, status, project_type, start_date, end_date, created_at, updated_at)
        VALUES (%(id)s, %(name)s, %(description)s, %(owner_id)s, %(status)s, %(project_type)s, %(start_date)s, %(end_date)s, %(created_at)s, %(updated_at)s)
        """
        
        for project in self.test_projects:
            cursor.execute(insert_sql, project)
        
        self.db_conn.commit()
        logger.info(f"成功插入 {len(self.test_projects)} 个项目")
    
    def insert_tasks(self):
        """插入任务数据"""
        logger.info("插入任务测试数据...")
        
        cursor = self.db_conn.cursor()
        
        # 插入任务数据
        insert_sql = """
        INSERT INTO tasks (id, title, description, project_id, assignee_id, status, priority, 
                          estimated_hours, actual_hours, due_date, created_at, updated_at)
        VALUES (%(id)s, %(title)s, %(description)s, %(project_id)s, %(assignee_id)s, %(status)s, 
                %(priority)s, %(estimated_hours)s, %(actual_hours)s, %(due_date)s, %(created_at)s, %(updated_at)s)
        """
        
        for task in self.test_tasks:
            cursor.execute(insert_sql, task)
        
        self.db_conn.commit()
        logger.info(f"成功插入 {len(self.test_tasks)} 个任务")
    
    def setup_redis_cache(self):
        """设置Redis缓存数据"""
        logger.info("设置Redis缓存数据...")
        
        # 缓存用户会话信息
        for user in self.test_users[:5]:  # 只为前5个用户创建会话
            session_key = f"session:{user['id']}"
            session_data = {
                "user_id": user["id"],
                "username": user["username"],
                "role": user["role"],
                "login_time": datetime.now().isoformat()
            }
            self.redis_conn.setex(session_key, 3600, json.dumps(session_data))
        
        # 缓存项目统计信息
        for project in self.test_projects:
            stats_key = f"project_stats:{project['id']}"
            project_tasks = [task for task in self.test_tasks if task["project_id"] == project["id"]]
            
            stats = {
                "total_tasks": len(project_tasks),
                "completed_tasks": len([t for t in project_tasks if t["status"] == "DONE"]),
                "in_progress_tasks": len([t for t in project_tasks if t["status"] == "IN_PROGRESS"]),
                "last_updated": datetime.now().isoformat()
            }
            self.redis_conn.setex(stats_key, 1800, json.dumps(stats))
        
        logger.info("Redis缓存数据设置完成")
    
    def generate_test_report(self):
        """生成测试数据报告"""
        logger.info("生成测试数据报告...")
        
        report = {
            "生成时间": datetime.now().isoformat(),
            "数据统计": {
                "用户数量": len(self.test_users),
                "项目数量": len(self.test_projects),
                "任务数量": len(self.test_tasks)
            },
            "测试账户": [
                {
                    "用户名": user["username"],
                    "邮箱": user["email"],
                    "密码": user["password"],
                    "角色": user["role"]
                }
                for user in self.test_users[:10]  # 只显示前10个用户
            ]
        }
        
        with open("uat_test_data_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("测试数据报告已生成: uat_test_data_report.json")
    
    def run(self):
        """执行完整的测试数据生成流程"""
        try:
            logger.info("开始生成UAT测试数据...")
            
            # 连接数据库
            self.connect_database()
            
            # 生成测试数据
            self.generate_test_users()
            self.generate_test_projects()
            self.generate_test_tasks()
            
            # 插入数据库
            self.insert_users()
            self.insert_projects()
            self.insert_tasks()
            
            # 设置缓存
            self.setup_redis_cache()
            
            # 生成报告
            self.generate_test_report()
            
            logger.info("UAT测试数据生成完成！")
            
        except Exception as e:
            logger.error(f"测试数据生成失败: {e}")
            raise
        finally:
            self.close_connections()

def main():
    """主函数"""
    # 数据库配置
    db_config = {
        "host": "localhost",
        "port": 5432,
        "database": "ai_pm_uat",
        "user": "ai_pm_user",
        "password": "uat_password_123"
    }
    
    # Redis配置
    redis_config = {
        "host": "localhost",
        "port": 6379,
        "db": 1,
        "decode_responses": True
    }
    
    # 创建数据生成器并运行
    generator = UATDataGenerator(db_config, redis_config)
    generator.run()

if __name__ == "__main__":
    main()
