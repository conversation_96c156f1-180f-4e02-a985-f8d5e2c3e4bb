#!/bin/bash

# AI项目管理平台生产环境部署脚本
# 版本: v1.0
# 创建时间: 2025-08-28
# 描述: 自动化部署生产环境，包括基础设施、应用服务和监控系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
PROD_NAMESPACE="ai-pm-prod"
PROD_DOMAIN="ai-pm.your-domain.com"
API_DOMAIN="api.ai-pm.your-domain.com"
ADMIN_DOMAIN="admin.ai-pm.your-domain.com"
DATABASE_NAME="ai_pm_prod"
BACKUP_BUCKET="ai-pm-backups"

# 镜像版本
USER_MANAGEMENT_IMAGE="ai-pm/user-management:v1.0.0"
PROJECT_MANAGEMENT_IMAGE="ai-pm/project-management:v1.0.0"
AI_ANALYSIS_IMAGE="ai-pm/ai-analysis:v1.0.0"
FRONTEND_IMAGE="ai-pm/frontend:v1.0.0"
NOTIFICATION_IMAGE="ai-pm/notification:v1.0.0"

# 检查必要的工具和权限
check_prerequisites() {
    log_info "检查部署前置条件..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装，请先安装 kubectl"
        exit 1
    fi
    
    # 检查helm
    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装，请先安装 helm"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查命名空间权限
    if ! kubectl auth can-i create namespaces; then
        log_error "没有创建命名空间的权限"
        exit 1
    fi
    
    log_success "前置条件检查完成"
}

# 创建生产命名空间
create_namespace() {
    log_info "创建生产环境命名空间..."
    
    kubectl create namespace $PROD_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # 设置命名空间标签
    kubectl label namespace $PROD_NAMESPACE environment=production --overwrite
    kubectl label namespace $PROD_NAMESPACE app=ai-pm --overwrite
    
    log_success "命名空间 $PROD_NAMESPACE 创建完成"
}

# 创建密钥和配置
create_secrets_and_configs() {
    log_info "创建生产环境密钥和配置..."
    
    # 数据库连接密钥
    kubectl create secret generic database-credentials \
        --namespace=$PROD_NAMESPACE \
        --from-literal=url="*********************************************************" \
        --from-literal=username="ai_pm_user" \
        --from-literal=password="$(openssl rand -base64 32)" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Redis连接密钥
    kubectl create secret generic redis-credentials \
        --namespace=$PROD_NAMESPACE \
        --from-literal=url="redis://redis-cluster:6379/0" \
        --from-literal=password="$(openssl rand -base64 32)" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # JWT密钥
    kubectl create secret generic jwt-secret \
        --namespace=$PROD_NAMESPACE \
        --from-literal=secret="$(openssl rand -base64 64)" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # SSL证书密钥（假设已有证书文件）
    if [ -f "ssl/tls.crt" ] && [ -f "ssl/tls.key" ]; then
        kubectl create secret tls ai-pm-tls \
            --namespace=$PROD_NAMESPACE \
            --cert=ssl/tls.crt \
            --key=ssl/tls.key \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    log_success "密钥和配置创建完成"
}

# 部署PostgreSQL集群
deploy_postgresql() {
    log_info "部署PostgreSQL集群..."
    
    # 安装CloudNativePG Operator（如果未安装）
    if ! kubectl get crd clusters.postgresql.cnpg.io &> /dev/null; then
        log_info "安装CloudNativePG Operator..."
        kubectl apply -f https://raw.githubusercontent.com/cloudnative-pg/cloudnative-pg/release-1.20/releases/cnpg-1.20.0.yaml
        
        # 等待Operator就绪
        kubectl wait --for=condition=Available deployment/cnpg-controller-manager \
            -n cnpg-system --timeout=300s
    fi
    
    # 部署PostgreSQL集群
    cat <<EOF | kubectl apply -f -
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgres-cluster
  namespace: $PROD_NAMESPACE
spec:
  instances: 3
  primaryUpdateStrategy: unsupervised
  
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
      maintenance_work_mem: "64MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
      
  bootstrap:
    initdb:
      database: $DATABASE_NAME
      owner: ai_pm_user
      secret:
        name: database-credentials
        
  storage:
    size: 200Gi
    storageClass: fast-ssd
    
  monitoring:
    enabled: true
    
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://$BACKUP_BUCKET/postgres"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY
EOF

    # 等待PostgreSQL集群就绪
    log_info "等待PostgreSQL集群就绪..."
    kubectl wait --for=condition=Ready cluster/postgres-cluster \
        -n $PROD_NAMESPACE --timeout=600s
    
    log_success "PostgreSQL集群部署完成"
}

# 部署Redis集群
deploy_redis() {
    log_info "部署Redis集群..."
    
    # 安装Redis Operator（如果未安装）
    if ! kubectl get crd redisclusters.redis.redis.opstreelabs.in &> /dev/null; then
        log_info "安装Redis Operator..."
        kubectl apply -f https://raw.githubusercontent.com/OT-CONTAINER-KIT/redis-operator/master/config/crd/bases/redis.redis.opstreelabs.in_redisclusters.yaml
        kubectl apply -f https://raw.githubusercontent.com/OT-CONTAINER-KIT/redis-operator/master/config/manager/manager.yaml
    fi
    
    # 部署Redis集群
    cat <<EOF | kubectl apply -f -
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: RedisCluster
metadata:
  name: redis-cluster
  namespace: $PROD_NAMESPACE
spec:
  clusterSize: 6
  clusterVersion: v7.0.12
  persistenceEnabled: true
  
  redisExporter:
    enabled: true
    image: oliver006/redis_exporter:latest
    
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 50Gi
        storageClassName: fast-ssd
        
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
      
  securityContext:
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
EOF

    # 等待Redis集群就绪
    log_info "等待Redis集群就绪..."
    kubectl wait --for=condition=Ready rediscluster/redis-cluster \
        -n $PROD_NAMESPACE --timeout=600s
    
    log_success "Redis集群部署完成"
}

# 部署应用服务
deploy_applications() {
    log_info "部署应用服务..."
    
    # 部署用户管理服务
    log_info "部署用户管理服务..."
    kubectl apply -f deployment/production/user-management.yaml
    
    # 部署项目管理服务
    log_info "部署项目管理服务..."
    kubectl apply -f deployment/production/project-management.yaml
    
    # 部署AI分析服务
    log_info "部署AI分析服务..."
    kubectl apply -f deployment/production/ai-analysis.yaml
    
    # 部署通知服务
    log_info "部署通知服务..."
    kubectl apply -f deployment/production/notification.yaml
    
    # 部署前端应用
    log_info "部署前端应用..."
    kubectl apply -f deployment/production/frontend.yaml
    
    log_success "应用服务部署完成"
}

# 配置Ingress和负载均衡
configure_ingress() {
    log_info "配置Ingress和负载均衡..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-pm-prod-ingress
  namespace: $PROD_NAMESPACE
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - $PROD_DOMAIN
    - $API_DOMAIN
    - $ADMIN_DOMAIN
    secretName: ai-pm-tls
  rules:
  - host: $PROD_DOMAIN
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  - host: $API_DOMAIN
    http:
      paths:
      - path: /api/v1/users
        pathType: Prefix
        backend:
          service:
            name: user-management-service
            port:
              number: 8080
      - path: /api/v1/projects
        pathType: Prefix
        backend:
          service:
            name: project-management-service
            port:
              number: 8081
      - path: /api/v1/ai
        pathType: Prefix
        backend:
          service:
            name: ai-analysis-service
            port:
              number: 8000
      - path: /api/v1/notifications
        pathType: Prefix
        backend:
          service:
            name: notification-service
            port:
              number: 8082
  - host: $ADMIN_DOMAIN
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: admin-service
            port:
              number: 80
EOF

    log_success "Ingress配置完成"
}

# 部署监控系统
deploy_monitoring() {
    log_info "部署监控系统..."
    
    # 部署Prometheus
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace $PROD_NAMESPACE \
        --values deployment/production/monitoring/prometheus-values.yaml \
        --wait
    
    # 部署自定义监控规则
    kubectl apply -f deployment/production/monitoring/prometheus-rules.yaml
    
    log_success "监控系统部署完成"
}

# 等待所有服务就绪
wait_for_services() {
    log_info "等待所有服务就绪..."
    
    # 等待所有部署就绪
    kubectl wait --for=condition=available deployment --all \
        -n $PROD_NAMESPACE --timeout=900s
    
    # 检查所有Pod状态
    kubectl get pods -n $PROD_NAMESPACE
    
    log_success "所有服务已就绪"
}

# 执行健康检查
health_check() {
    log_info "执行生产环境健康检查..."
    
    # 检查数据库连接
    log_info "检查数据库连接..."
    kubectl exec -n $PROD_NAMESPACE deployment/user-management -- \
        curl -f http://localhost:8080/actuator/health/db || {
        log_error "数据库连接检查失败"
        return 1
    }
    
    # 检查Redis连接
    log_info "检查Redis连接..."
    kubectl exec -n $PROD_NAMESPACE deployment/user-management -- \
        curl -f http://localhost:8080/actuator/health/redis || {
        log_error "Redis连接检查失败"
        return 1
    }
    
    # 检查API响应
    log_info "检查API响应..."
    for service in user-management project-management ai-analysis; do
        kubectl exec -n $PROD_NAMESPACE deployment/$service -- \
            curl -f http://localhost:808*/actuator/health || {
            log_error "$service 健康检查失败"
            return 1
        }
    done
    
    # 检查前端访问
    log_info "检查前端访问..."
    if command -v curl &> /dev/null; then
        curl -f -k https://$PROD_DOMAIN/health || {
            log_warning "前端健康检查失败，可能DNS还未生效"
        }
    fi
    
    log_success "健康检查完成"
}

# 数据迁移
migrate_data() {
    log_info "执行数据迁移..."
    
    # 从UAT环境导出数据（如果需要）
    if [ "$MIGRATE_FROM_UAT" = "true" ]; then
        log_info "从UAT环境迁移数据..."
        # 这里添加数据迁移逻辑
        kubectl exec -n ai-pm-uat deployment/user-management -- \
            pg_dump -h postgres-service -U ai_pm_user ai_pm_uat > uat_backup.sql
        
        # 导入到生产环境
        kubectl exec -n $PROD_NAMESPACE postgres-cluster-1 -- \
            psql -U ai_pm_user -d $DATABASE_NAME < uat_backup.sql
    fi
    
    log_success "数据迁移完成"
}

# 主函数
main() {
    log_info "开始部署AI项目管理平台生产环境..."
    
    check_prerequisites
    create_namespace
    create_secrets_and_configs
    deploy_postgresql
    deploy_redis
    deploy_applications
    configure_ingress
    deploy_monitoring
    wait_for_services
    
    if [ "$MIGRATE_DATA" = "true" ]; then
        migrate_data
    fi
    
    health_check
    
    log_success "生产环境部署完成！"
    log_info "访问地址:"
    log_info "  主站: https://$PROD_DOMAIN"
    log_info "  API: https://$API_DOMAIN"
    log_info "  管理: https://$ADMIN_DOMAIN"
    log_info "请等待DNS解析生效后访问系统"
}

# 回滚函数
rollback() {
    log_warning "执行生产环境回滚..."
    
    # 回滚所有部署到上一个版本
    kubectl rollout undo deployment --all -n $PROD_NAMESPACE
    
    # 等待回滚完成
    kubectl rollout status deployment --all -n $PROD_NAMESPACE
    
    # 验证回滚后状态
    health_check
    
    log_success "回滚完成"
}

# 清理函数
cleanup() {
    log_warning "清理生产环境..."
    read -p "确认要删除生产环境吗？(yes/no): " confirm
    if [ "$confirm" = "yes" ]; then
        kubectl delete namespace $PROD_NAMESPACE --ignore-not-found=true
        log_success "生产环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 脚本参数处理
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "cleanup")
        cleanup
        ;;
    "health")
        health_check
        ;;
    "migrate")
        MIGRATE_DATA=true
        main
        ;;
    *)
        echo "用法: $0 [deploy|rollback|cleanup|health|migrate]"
        echo "  deploy   - 部署生产环境（默认）"
        echo "  rollback - 回滚到上一个版本"
        echo "  cleanup  - 清理生产环境"
        echo "  health   - 健康检查"
        echo "  migrate  - 部署并迁移数据"
        exit 1
        ;;
esac
